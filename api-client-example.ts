// 🚀 Schedule Management System - API Client Example
// This file demonstrates how to use the API with TypeScript/JavaScript

import { 
  ApiResponse, 
  LoginRequest, 
  LoginResponse, 
  ScheduleRequest, 
  ScheduleResponse,
  PageResponse,
  DashboardResponse,
  API_ENDPOINTS 
} from './api-types';

// ============================================================================
// API CLIENT CLASS
// ============================================================================

class ScheduleManagementAPI {
  private baseURL: string;
  private token: string | null = null;

  constructor(baseURL: string = 'http://localhost:8080/api') {
    this.baseURL = baseURL;
  }

  // Set authentication token
  setToken(token: string) {
    this.token = token;
  }

  // Get authentication headers
  private getHeaders(): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`;
    }

    return headers;
  }

  // Generic API request method
  private async request<T>(
    endpoint: string, 
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;
    
    const response = await fetch(url, {
      ...options,
      headers: {
        ...this.getHeaders(),
        ...options.headers,
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.json();
  }

  // ============================================================================
  // AUTHENTICATION METHODS
  // ============================================================================

  async login(credentials: LoginRequest): Promise<ApiResponse<LoginResponse>> {
    const response = await this.request<LoginResponse>(API_ENDPOINTS.LOGIN, {
      method: 'POST',
      body: JSON.stringify(credentials),
    });

    // Automatically set token after successful login
    if (response.success && response.data.token) {
      this.setToken(response.data.token);
    }

    return response;
  }

  async logout(): Promise<ApiResponse<string>> {
    const response = await this.request<string>(API_ENDPOINTS.LOGOUT, {
      method: 'POST',
    });

    // Clear token after logout
    this.token = null;
    return response;
  }

  async getCurrentUser(): Promise<ApiResponse<any>> {
    return this.request<any>(API_ENDPOINTS.CURRENT_USER);
  }

  // ============================================================================
  // SCHEDULE METHODS
  // ============================================================================

  async createSchedule(schedule: ScheduleRequest): Promise<ApiResponse<ScheduleResponse>> {
    return this.request<ScheduleResponse>(API_ENDPOINTS.SCHEDULES, {
      method: 'POST',
      body: JSON.stringify(schedule),
    });
  }

  async getSchedulesByTeacher(
    teacherId: number, 
    semesterId?: number
  ): Promise<ApiResponse<ScheduleResponse[]>> {
    const params = new URLSearchParams();
    if (semesterId) params.append('semesterId', semesterId.toString());
    
    const endpoint = `${API_ENDPOINTS.SCHEDULE_BY_TEACHER}/${teacherId}?${params}`;
    return this.request<ScheduleResponse[]>(endpoint);
  }

  async getPersonalSchedule(semesterId?: number): Promise<ApiResponse<ScheduleResponse[]>> {
    const params = new URLSearchParams();
    if (semesterId) params.append('semesterId', semesterId.toString());
    
    const endpoint = `${API_ENDPOINTS.PERSONAL_SCHEDULE}?${params}`;
    return this.request<ScheduleResponse[]>(endpoint);
  }

  async checkScheduleConflict(request: any): Promise<ApiResponse<any>> {
    return this.request<any>(API_ENDPOINTS.CHECK_CONFLICT, {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  async getAvailableRooms(params: {
    coSoId: number;
    loaiPhong: string;
    thuHoc: number;
    buoiId: number;
    hocKyId: number;
  }): Promise<ApiResponse<any[]>> {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      searchParams.append(key, value.toString());
    });
    
    const endpoint = `${API_ENDPOINTS.AVAILABLE_ROOMS}?${searchParams}`;
    return this.request<any[]>(endpoint);
  }

  async deleteSchedule(id: number): Promise<ApiResponse<string>> {
    return this.request<string>(`${API_ENDPOINTS.SCHEDULES}/${id}`, {
      method: 'DELETE',
    });
  }

  // ============================================================================
  // MASTER DATA METHODS
  // ============================================================================

  async getDepartments(page = 0, size = 10): Promise<ApiResponse<PageResponse<any>>> {
    const params = new URLSearchParams({ 
      page: page.toString(), 
      size: size.toString() 
    });
    return this.request<PageResponse<any>>(`${API_ENDPOINTS.DEPARTMENTS}?${params}`);
  }

  async getSubjects(departmentId?: number, page = 0, size = 10): Promise<ApiResponse<PageResponse<any>>> {
    const params = new URLSearchParams({ 
      page: page.toString(), 
      size: size.toString() 
    });
    if (departmentId) params.append('departmentId', departmentId.toString());
    
    return this.request<PageResponse<any>>(`${API_ENDPOINTS.SUBJECTS}?${params}`);
  }

  async getClasses(departmentId?: number, page = 0, size = 10): Promise<ApiResponse<PageResponse<any>>> {
    const params = new URLSearchParams({ 
      page: page.toString(), 
      size: size.toString() 
    });
    if (departmentId) params.append('departmentId', departmentId.toString());
    
    return this.request<PageResponse<any>>(`${API_ENDPOINTS.CLASSES}?${params}`);
  }

  async getTeachers(departmentId?: number, page = 0, size = 10): Promise<ApiResponse<PageResponse<any>>> {
    const params = new URLSearchParams({ 
      page: page.toString(), 
      size: size.toString() 
    });
    if (departmentId) params.append('departmentId', departmentId.toString());
    
    return this.request<PageResponse<any>>(`${API_ENDPOINTS.TEACHERS}?${params}`);
  }

  async getRooms(campusId?: number, page = 0, size = 10): Promise<ApiResponse<PageResponse<any>>> {
    const params = new URLSearchParams({ 
      page: page.toString(), 
      size: size.toString() 
    });
    if (campusId) params.append('campusId', campusId.toString());
    
    return this.request<PageResponse<any>>(`${API_ENDPOINTS.ROOMS}?${params}`);
  }

  async getCampuses(): Promise<ApiResponse<any[]>> {
    return this.request<any[]>(API_ENDPOINTS.CAMPUSES);
  }

  // ============================================================================
  // ADMIN METHODS
  // ============================================================================

  async getDashboard(): Promise<ApiResponse<DashboardResponse>> {
    return this.request<DashboardResponse>(API_ENDPOINTS.DASHBOARD);
  }

  async getStatistics(): Promise<ApiResponse<any>> {
    return this.request<any>(API_ENDPOINTS.STATISTICS);
  }

  // ============================================================================
  // HEALTH CHECK METHODS
  // ============================================================================

  async healthCheck(): Promise<ApiResponse<any>> {
    return this.request<any>(API_ENDPOINTS.HEALTH);
  }

  async getSystemInfo(): Promise<ApiResponse<any>> {
    return this.request<any>(API_ENDPOINTS.SYSTEM_INFO);
  }
}

// ============================================================================
// USAGE EXAMPLES
// ============================================================================

// Create API client instance
const api = new ScheduleManagementAPI();

// Example: Login and get user info
async function loginExample() {
  try {
    // Login
    const loginResponse = await api.login({
      maCanBo: 'admin',
      matKhau: '123456'
    });

    if (loginResponse.success) {
      console.log('Login successful:', loginResponse.data.userInfo);
      
      // Get current user info
      const userResponse = await api.getCurrentUser();
      console.log('Current user:', userResponse.data);
    }
  } catch (error) {
    console.error('Login failed:', error);
  }
}

// Example: Get dashboard data
async function dashboardExample() {
  try {
    const response = await api.getDashboard();
    if (response.success) {
      console.log('Dashboard data:', response.data);
    }
  } catch (error) {
    console.error('Failed to get dashboard:', error);
  }
}

// Example: Create a schedule
async function createScheduleExample() {
  try {
    const scheduleData: ScheduleRequest = {
      idLop: 1,
      idHinhThuc: 1,
      idMonHoc: 1,
      soTiet: 4,
      heSo: 1.0,
      idCanBo: 3,
      thuHoc: 2,
      idBuoi: 1,
      idPhong: 1,
      tuanHoc: '1-15',
      idHocKy: 1
    };

    const response = await api.createSchedule(scheduleData);
    if (response.success) {
      console.log('Schedule created:', response.data);
    }
  } catch (error) {
    console.error('Failed to create schedule:', error);
  }
}

// Example: Get personal schedule
async function getPersonalScheduleExample() {
  try {
    const response = await api.getPersonalSchedule(1); // semesterId = 1
    if (response.success) {
      console.log('Personal schedule:', response.data);
    }
  } catch (error) {
    console.error('Failed to get personal schedule:', error);
  }
}

// Export the API client class and examples
export { 
  ScheduleManagementAPI, 
  loginExample, 
  dashboardExample, 
  createScheduleExample, 
  getPersonalScheduleExample 
};

// Default export
export default ScheduleManagementAPI;
