// 📚 Schedule Management System - API Types
// Generated from backend API documentation

// ============================================================================
// REQUEST TYPES
// ============================================================================

export interface LoginRequest {
  maCanBo: string;
  matKhau: string;
}

export interface ChangePasswordRequest {
  matKhauCu: string;
  matKhauMoi: string;
  xacNhanMatKhau: string;
}

export interface ScheduleRequest {
  idLop: number;
  idHinhThuc: number;
  nhomTh?: string;
  idMonHoc: number;
  soTiet: number;
  heSo: number;
  idCanBo: number;
  thuHoc: number;
  idBuoi: number;
  idPhong: number;
  tuanHoc?: string;
  ghiChu?: string;
  idHocKy: number;
}

export interface AcademicYearRequest {
  tenNienKhoa: string;
  nam: number;
  idThongTu?: number;
  moTa?: string;
  trangThai: boolean;
}

export interface SemesterRequest {
  idNienKhoa: number;
  tenHocKy: string;
  soTuan: number;
  ngayBatDau: string;
  ngayKetThuc: string;
  hienTai: boolean;
  moTa?: string;
  trangThai: boolean;
}

export interface TeachingHourRequest {
  teacherId: number;
  semesterId: number;
  year?: number;
  month?: number;
}

export interface FilterRequest {
  keyword?: string;
  departmentId?: number;
  semesterId?: number;
  educationLevelId?: number;
  majorId?: number;
  classId?: number;
  teacherId?: number;
  fromDate?: string;
  toDate?: string;
  status?: boolean;
  sortBy: string;
  sortDirection: string;
  page: number;
  size: number;
}

export interface ConflictCheckRequest {
  idCanBo: number;
  idPhong: number;
  idLop: number;
  thuHoc: number;
  idBuoi: number;
  idHocKy: number;
}

// ============================================================================
// RESPONSE TYPES
// ============================================================================

export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
  metadata?: any;
}

export interface PageResponse<T> {
  content: T[];
  page: number;
  size: number;
  totalElements: number;
  totalPages: number;
  first: boolean;
  last: boolean;
  hasNext: boolean;
  hasPrevious: boolean;
}

export interface UserInfo {
  id: number;
  maCanBo: string;
  ten: string;
  email: string;
  sdt: string;
  tenVaiTro: string;
  tenKhoa: string;
  nu: boolean;
}

export interface LoginResponse {
  token: string;
  tokenType: string;
  expiresIn: number;
  userInfo: UserInfo;
}

export interface ScheduleResponse {
  id: number;
  maMonHoc: string;
  tenMonHoc: string;
  tenGiangVien: string;
  maLop: string;
  tenLop: string;
  hinhThucHoc: string;
  thuHoc: number;
  thuHocText: string;
  tenBuoi: string;
  gioBatDau: string;
  gioKetThuc: string;
  tenPhong: string;
  tenCoSo: string;
  soTiet: number;
  heSo: number;
  soGioQuyDoi: number;
  nhomTh?: string;
  tuanHoc?: string;
  ghiChu?: string;
  trangThai: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface TeachingHourResponse {
  teacherId: number;
  maCanBo: string;
  tenCanBo: string;
  tenKhoa: string;
  semesterId: number;
  tenHocKy: string;
  tongGioLt: number;
  tongGioTh: number;
  tongGioTong: number;
  tongGioQuyDoi: number;
  tyLePhanBo: string;
  ngayTinh: string;
  lichGiangList: ScheduleResponse[];
}

export interface AcademicYearResponse {
  idNienKhoa: number;
  tenNienKhoa: string;
  nam: number;
  idThongTu?: number;
  moTa?: string;
  trangThai: boolean;
  ngayTao: string;
  ngayCapNhat: string;
  soHocKy: number;
  soLichGiang: number;
  isActive: boolean;
  hocKyList?: SemesterResponse[];
}

export interface SemesterResponse {
  idHocKy: number;
  idNienKhoa: number;
  tenHocKy: string;
  soTuan: number;
  ngayBatDau: string;
  ngayKetThuc: string;
  hienTai: boolean;
  moTa?: string;
  trangThai: boolean;
  ngayTao: string;
  ngayCapNhat: string;
  tenNienKhoa: string;
  namNienKhoa: number;
  soLichGiang: number;
  soGiangVien: number;
  soLopHoc: number;
  soMonHoc: number;
  trangThaiHocKy: string;
  soNgayConLai: number;
}

export interface DepartmentStats {
  departmentId: number;
  departmentName: string;
  totalTeachers: number;
  totalSubjects: number;
  totalTeachingHours: number;
}

export interface MonthlyStats {
  monthName: string;
  totalSchedules: number;
  totalTeachingHours: number;
}

export interface DashboardResponse {
  totalTeachers: number;
  totalSubjects: number;
  totalClasses: number;
  totalSchedules: number;
  totalRooms: number;
  totalDepartments: number;
  totalAcademicYears: number;
  totalSemesters: number;
  totalTeachingHours: number;
  currentSemesterId: number;
  currentSemester: string;
  currentSemesterName: string;
  weeklySchedules: number;
  departmentStats: DepartmentStats[];
  monthlyStats: MonthlyStats[];
}

export interface ConflictDetail {
  type: string;
  description: string;
  scheduleId: number;
  scheduleName: string;
}

export interface ConflictCheckResponse {
  hasConflict: boolean;
  message: string;
  conflictType: string;
  conflicts: ConflictDetail[];
}

export interface AvailableRoomResponse {
  roomId: number;
  maPhong: string;
  tenPhong: string;
  loaiPhong: string;
  sucChua: number;
  tenCoSo: string;
  maCoSo: string;
  isAvailable: boolean;
  description: string;
}

export interface HealthResponse {
  status: string;
  timestamp: string;
  service: string;
  version: string;
}

export interface SystemInfoResponse {
  name: string;
  description: string;
  version: string;
  'java.version': string;
  'spring.version': string;
  timestamp: string;
}

// ============================================================================
// MASTER DATA TYPES
// ============================================================================

export interface Department {
  id: number;
  maPbmm: string;
  tenPbmm: string;
  moTa?: string;
  trangThai: boolean;
}

export interface Subject {
  id: number;
  maMonHoc: string;
  tenMonHoc: string;
  soTinChi: number;
  soTietLt: number;
  soTietTh: number;
  idPbmm: number;
  tenPbmm: string;
  trangThai: boolean;
}

export interface ClassInfo {
  id: number;
  maLop: string;
  tenLop: string;
  idPbmm: number;
  tenPbmm: string;
  idNganhHoc: number;
  tenNganhHoc: string;
  idHeDaoTao: number;
  tenHeDaoTao: string;
  khoaHoc: number;
  siSo: number;
  trangThai: boolean;
}

export interface Teacher {
  id: number;
  maCanBo: string;
  ten: string;
  idVaiTro: number;
  tenVaiTro: string;
  idPbmm: number;
  tenPbmm: string;
  ngaySinh?: string;
  nu: boolean;
  sdt?: string;
  email?: string;
  trangThai: boolean;
}

export interface Room {
  id: number;
  maPhong: string;
  tenPhong: string;
  loaiPhong: string;
  sucChua: number;
  idCoSo: number;
  tenCoSo: string;
  maCoSo: string;
  trangThai: boolean;
}

export interface Campus {
  id: number;
  maCoSo: string;
  tenCoSo: string;
  diaChi?: string;
  trangThai: boolean;
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

export type UserRole = 'ADMIN' | 'TRUONG_KHOA' | 'GIANG_VIEN';

export type SemesterStatus = 'CHUA_BAT_DAU' | 'DANG_DIEN_RA' | 'DA_KET_THUC';

export type ConflictType = 'TEACHER' | 'ROOM' | 'CLASS';

export type SortDirection = 'ASC' | 'DESC';

export type RoomType = 'LT' | 'TH' | 'HT';

// ============================================================================
// API ENDPOINT CONSTANTS
// ============================================================================

export const API_ENDPOINTS = {
  // Authentication
  LOGIN: '/auth/login',
  LOGOUT: '/auth/logout',
  CHANGE_PASSWORD: '/auth/change-password',
  CURRENT_USER: '/auth/me',
  
  // Schedules
  SCHEDULES: '/schedules',
  SCHEDULE_BY_TEACHER: '/schedules/teacher',
  SCHEDULE_BY_CLASS: '/schedules/class',
  SCHEDULE_BY_SEMESTER: '/schedules/semester',
  PERSONAL_SCHEDULE: '/schedules/personal',
  CHECK_CONFLICT: '/schedules/check-conflict',
  AVAILABLE_ROOMS: '/schedules/available-rooms',
  EXPORT_PERSONAL: '/schedules/export/personal',
  EXPORT_TEACHER: '/schedules/export/teacher',
  
  // Teaching Hours
  TEACHING_HOURS: '/teaching-hours',
  CALCULATE_HOURS: '/teaching-hours/calculate',
  HOURS_BY_TEACHER: '/teaching-hours/teacher',
  HOURS_BY_DEPARTMENT: '/teaching-hours/department',
  PERSONAL_HOURS: '/teaching-hours/personal',
  EXPORT_HOURS: '/teaching-hours/export',
  
  // Academic Years
  ACADEMIC_YEARS: '/academic-years',
  
  // Semesters
  SEMESTERS: '/semesters',
  SET_CURRENT_SEMESTER: '/semesters/{id}/set-current',
  SEMESTER_STATISTICS: '/semesters/{id}/statistics',
  
  // Master Data
  DEPARTMENTS: '/master-data/departments',
  SUBJECTS: '/master-data/subjects',
  CLASSES: '/master-data/classes',
  TEACHERS: '/master-data/teachers',
  ROOMS: '/master-data/rooms',
  CAMPUSES: '/master-data/campuses',
  IMPORT_DATA: '/master-data/import',
  
  // Admin
  DASHBOARD: '/admin/dashboard',
  STATISTICS: '/admin/statistics',
  BACKUP: '/admin/backup',
  
  // Import/Sync
  SYNC_DATA: '/import/sync',
  UPLOAD_FILE: '/import/upload',
  IMPORT_STATUS: '/import/status',
  TEST_CONNECTION: '/import/test-connection',
  
  // Health
  HEALTH: '/health',
  SYSTEM_INFO: '/health/info'
} as const;
